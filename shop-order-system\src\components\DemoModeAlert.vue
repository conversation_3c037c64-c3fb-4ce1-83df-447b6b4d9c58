<template>
  <el-alert
    v-if="!isSupabaseConfigured"
    title="演示模式"
    type="warning"
    :closable="false"
    show-icon
    style="margin-bottom: 20px;"
  >
    <template #default>
      <p>当前运行在演示模式下，数据仅供展示，不会保存到数据库。</p>
      <p>
        <strong>演示账号：</strong>
        <br>
        管理员：<EMAIL> / demo123
        <br>
        员工：<EMAIL> / demo123
      </p>
      <p>
        要启用完整功能，请配置Supabase：
        <el-button type="text" @click="showConfig = !showConfig">
          {{ showConfig ? '隐藏' : '查看' }}配置说明
        </el-button>
      </p>
      <div v-if="showConfig" style="margin-top: 10px; padding: 10px; background: #f5f7fa; border-radius: 4px;">
        <ol>
          <li>访问 <a href="https://supabase.com" target="_blank">Supabase</a> 创建项目</li>
          <li>复制项目URL和API密钥</li>
          <li>在项目根目录的 .env 文件中配置：
            <pre style="margin: 5px 0; padding: 5px; background: white; border-radius: 2px;">
VITE_SUPABASE_URL=你的项目URL
VITE_SUPABASE_ANON_KEY=你的API密钥</pre>
          </li>
          <li>重启开发服务器</li>
        </ol>
      </div>
    </template>
  </el-alert>
</template>

<script setup>
import { ref } from 'vue'
import { isSupabaseConfigured } from '../lib/supabase'

const showConfig = ref(false)
</script>

<style scoped>
pre {
  font-size: 12px;
  color: #606266;
}

a {
  color: #409EFF;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}
</style>
