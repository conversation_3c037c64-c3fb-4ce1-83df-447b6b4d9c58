package models

import (
	"time"
)

// Product 商品模型
type Product struct {
	ID               int64                  `json:"id" xorm:"pk autoincr 'id'"`
	Name             string                 `json:"name" xorm:"varchar(255) notnull 'name'"`
	Category         string                 `json:"category" xorm:"varchar(255) 'category'"`
	Price            float64                `json:"price" xorm:"decimal(10,2) notnull 'price'"`
	IsListed         bool                   `json:"is_listed" xorm:"bool notnull default(true) 'is_listed'"`
	ImageURL         string                 `json:"image_url" xorm:"text 'image_url'"`
	CustomAttributes map[string]interface{} `json:"custom_attributes" xorm:"json 'custom_attributes'"`
	CreatedAt        time.Time              `json:"created_at" xorm:"created 'created_at'"`
	UpdatedAt        time.Time              `json:"updated_at" xorm:"updated 'updated_at'"`
}

// TableName 指定表名
func (Product) TableName() string {
	return "products"
}

// ProductRequest 商品请求
type ProductRequest struct {
	Name             string                 `json:"name" binding:"required"`
	Category         string                 `json:"category"`
	Price            float64                `json:"price" binding:"required,gt=0"`
	IsListed         bool                   `json:"is_listed"`
	ImageURL         string                 `json:"image_url"`
	CustomAttributes map[string]interface{} `json:"custom_attributes"`
}
