<template>
  <div class="products-view">
    <div class="page-header">
      <h2>商品管理</h2>
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        新增商品
      </el-button>
    </div>

    <el-card>
      <div class="search-bar">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchQuery"
              placeholder="搜索商品名称或类别"
              prefix-icon="Search"
              clearable
            />
          </el-col>
          <el-col :span="6">
            <el-select v-model="statusFilter" placeholder="筛选状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="上架" value="true" />
              <el-option label="下架" value="false" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="categoryFilter" placeholder="筛选类别" clearable>
              <el-option label="全部" value="" />
              <el-option
                v-for="category in availableCategories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-col>
        </el-row>
      </div>

      <el-table :data="filteredProducts" style="width: 100%" v-loading="productsStore.loading">
        <el-table-column label="图片" width="80">
          <template #default="scope">
            <el-avatar
              :size="50"
              :src="scope.row.image_url"
              icon="Picture"
            />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="商品名称" />
        <el-table-column prop="category" label="类别" />
        <el-table-column prop="price" label="价格">
          <template #default="scope">
            ¥{{ scope.row.price }}
          </template>
        </el-table-column>
        <el-table-column prop="is_listed" label="状态">
          <template #default="scope">
            <el-switch
              v-model="scope.row.is_listed"
              active-text="上架"
              inactive-text="下架"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="editProduct(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteProduct(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑商品对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingProduct ? '编辑商品' : '新增商品'"
      width="600px"
    >
      <el-form
        ref="productFormRef"
        :model="productForm"
        :rules="productRules"
        label-width="100px"
      >
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="productForm.name" />
        </el-form-item>
        
        <el-form-item label="类别" prop="category">
          <el-input v-model="productForm.category" />
        </el-form-item>
        
        <el-form-item label="价格" prop="price">
          <el-input-number
            v-model="productForm.price"
            :min="0"
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-switch
            v-model="productForm.is_listed"
            active-text="上架"
            inactive-text="下架"
          />
        </el-form-item>
        
        <el-form-item label="商品图片">
          <el-upload
            class="avatar-uploader"
            :http-request="handleAvatarUpload"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="productForm.image_url" :src="productForm.image_url" class="avatar" />
            <div v-else class="avatar-uploader-icon" v-loading="uploading">
              <el-icon><Plus /></el-icon>
            </div>
          </el-upload>
        </el-form-item>

        <!-- 自定义属性 -->
        <el-form-item label="自定义属性">
          <div class="custom-attributes">
            <div v-for="(attr, attrIndex) in customAttributes" :key="attrIndex" class="attribute-item">
              <el-input
                v-model="attr.key"
                placeholder="属性名称（如：规格、口味）"
                style="width: 150px; margin-right: 10px;"
              />
              <div class="attribute-values">
                <el-tag
                  v-for="(value, valueIndex) in attr.values"
                  :key="valueIndex"
                  closable
                  @close="removeAttributeValue(attrIndex, valueIndex)"
                  style="margin-right: 5px; margin-bottom: 5px;"
                >
                  <el-input
                    v-model="attr.values[valueIndex]"
                    size="small"
                    style="width: 80px;"
                    placeholder="值"
                  />
                </el-tag>
                <el-button
                  size="small"
                  type="primary"
                  plain
                  @click="addAttributeValue(attrIndex)"
                >
                  + 添加值
                </el-button>
              </div>
              <el-button
                type="danger"
                size="small"
                plain
                @click="removeCustomAttribute(attrIndex)"
                style="margin-left: 10px;"
              >
                删除属性
              </el-button>
            </div>
            <el-button type="primary" plain @click="addCustomAttribute">
              + 添加自定义属性
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveProduct">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useProductsStore } from '../stores/products'

const productsStore = useProductsStore()

const showAddDialog = ref(false)
const editingProduct = ref(null)
const productFormRef = ref()
const searchQuery = ref('')
const statusFilter = ref('')
const categoryFilter = ref('')
const uploading = ref(false)

const productForm = reactive({
  name: '',
  category: '',
  price: 0,
  is_listed: true,
  image_url: '',
  custom_attributes: {}
})

const productRules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请输入商品类别', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入商品价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能为负数', trigger: 'blur' }
  ]
}

// 自定义属性管理
const customAttributes = ref([])

const addCustomAttribute = () => {
  customAttributes.value.push({ key: '', values: [''] })
}

const removeCustomAttribute = (index) => {
  customAttributes.value.splice(index, 1)
}

const addAttributeValue = (attrIndex) => {
  customAttributes.value[attrIndex].values.push('')
}

const removeAttributeValue = (attrIndex, valueIndex) => {
  customAttributes.value[attrIndex].values.splice(valueIndex, 1)
}

const filteredProducts = computed(() => {
  return productsStore.searchProducts(searchQuery.value, {
    category: categoryFilter.value,
    status: statusFilter.value === '' ? undefined : statusFilter.value === 'true'
  })
})

const availableCategories = computed(() => {
  return productsStore.productCategories
})

const handleStatusChange = async (product) => {
  const result = await productsStore.toggleProductStatus(product.id, product.is_listed)
  if (!result.success) {
    // 回滚状态
    product.is_listed = !product.is_listed
  }
}

const editProduct = (product) => {
  editingProduct.value = product
  Object.assign(productForm, {
    name: product.name,
    category: product.category,
    price: product.price,
    is_listed: product.is_listed,
    image_url: product.image_url,
    custom_attributes: product.custom_attributes || {}
  })

  // 转换自定义属性为编辑格式
  customAttributes.value = Object.entries(product.custom_attributes || {}).map(([key, values]) => ({
    key,
    values: Array.isArray(values) ? values : [values]
  }))

  showAddDialog.value = true
}

const deleteProduct = async (product) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除商品"${product.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await productsStore.deleteProduct(product.id)
  } catch (error) {
    if (error.message) {
      console.error('删除商品失败:', error)
    }
    // 用户取消删除或删除失败
  }
}

const saveProduct = async () => {
  if (!productFormRef.value) return

  await productFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 处理自定义属性
        const attributes = {}
        customAttributes.value.forEach(attr => {
          if (attr.key.trim()) {
            attributes[attr.key.trim()] = attr.values.filter(v => v.trim())
          }
        })

        const productData = {
          ...productForm,
          custom_attributes: attributes
        }

        let result
        if (editingProduct.value) {
          // 编辑模式
          result = await productsStore.updateProduct(editingProduct.value.id, productData)
        } else {
          // 新增模式
          result = await productsStore.createProduct(productData)
        }

        if (result.success) {
          showAddDialog.value = false
          resetForm()
        }
      } catch (error) {
        console.error('保存商品失败:', error)
      }
    }
  })
}

const resetForm = () => {
  editingProduct.value = null
  Object.assign(productForm, {
    name: '',
    category: '',
    price: 0,
    is_listed: true,
    image_url: '',
    custom_attributes: {}
  })
  customAttributes.value = []
}

const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleAvatarUpload = async (options) => {
  const { file } = options

  if (!beforeAvatarUpload(file)) {
    return
  }

  try {
    uploading.value = true

    // 如果是编辑模式且有商品ID，使用商品ID；否则使用临时ID
    const productId = editingProduct.value?.id || `temp-${Date.now()}`
    const result = await productsStore.uploadProductImage(file, productId)

    if (result.success) {
      productForm.image_url = result.url
      ElMessage.success('图片上传成功')
    }
  } catch (error) {
    console.error('上传图片失败:', error)
  } finally {
    uploading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  productsStore.fetchProducts()
})
</script>

<style scoped>
.products-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
}

.search-bar {
  margin-bottom: 20px;
}

.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.custom-attributes {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.attribute-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  padding: 10px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.attribute-values {
  flex: 1;
  margin: 0 10px;
}

.attribute-values .el-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>
