package controllers

import (
	"net/http"
	"strconv"

	"shop-order-backend/internal/models"

	"github.com/gin-gonic/gin"
	"xorm.io/xorm"
)

type ProductController struct {
	db *xorm.Engine
}

func NewProductController(db *xorm.Engine) *ProductController {
	return &ProductController{db: db}
}

// GetProducts 获取商品列表
func (pc *ProductController) GetProducts(c *gin.Context) {
	var products []models.Product
	
	// 查询参数
	category := c.Query("category")
	isListed := c.Query("is_listed")
	
	session := pc.db.NewSession()
	defer session.Close()
	
	// 根据查询参数过滤
	if category != "" {
		session = session.Where("category = ?", category)
	}
	
	if isListed != "" {
		if isListed == "true" {
			session = session.Where("is_listed = ?", true)
		} else if isListed == "false" {
			session = session.Where("is_listed = ?", false)
		}
	}
	
	if err := session.Find(&products); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch products",
		})
		return
	}

	c.J<PERSON>(http.StatusOK, gin.H{
		"data": products,
	})
}

// GetProduct 获取单个商品信息
func (pc *ProductController) GetProduct(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid product ID",
		})
		return
	}

	var product models.Product
	has, err := pc.db.ID(id).Get(&product)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Product not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": product,
	})
}

// CreateProduct 创建商品
func (pc *ProductController) CreateProduct(c *gin.Context) {
	var req models.ProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	product := &models.Product{
		Name:             req.Name,
		Category:         req.Category,
		Price:            req.Price,
		IsListed:         req.IsListed,
		ImageURL:         req.ImageURL,
		CustomAttributes: req.CustomAttributes,
	}

	if _, err := pc.db.Insert(product); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create product",
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"data": product,
	})
}

// UpdateProduct 更新商品信息
func (pc *ProductController) UpdateProduct(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid product ID",
		})
		return
	}

	var req models.ProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 检查商品是否存在
	var product models.Product
	has, err := pc.db.ID(id).Get(&product)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Product not found",
		})
		return
	}

	// 更新商品信息
	product.Name = req.Name
	product.Category = req.Category
	product.Price = req.Price
	product.IsListed = req.IsListed
	product.ImageURL = req.ImageURL
	product.CustomAttributes = req.CustomAttributes

	if _, err := pc.db.ID(id).Update(&product); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update product",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": product,
	})
}

// DeleteProduct 删除商品
func (pc *ProductController) DeleteProduct(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid product ID",
		})
		return
	}

	// 检查商品是否存在
	var product models.Product
	has, err := pc.db.ID(id).Get(&product)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Product not found",
		})
		return
	}

	// 删除商品
	if _, err := pc.db.ID(id).Delete(&models.Product{}); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete product",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Product deleted successfully",
	})
}

// GetCategories 获取商品分类列表
func (pc *ProductController) GetCategories(c *gin.Context) {
	var categories []string
	
	if err := pc.db.Table("products").Distinct("category").Where("category != ''").Find(&categories); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch categories",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": categories,
	})
}
