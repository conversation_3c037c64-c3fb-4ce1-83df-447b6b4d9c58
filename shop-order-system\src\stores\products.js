import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase, isSupabaseConfigured } from '../lib/supabase'
import { ElMessage } from 'element-plus'
import { demoProducts } from '../lib/demo-data'

export const useProductsStore = defineStore('products', () => {
  // 状态
  const products = ref([])
  const loading = ref(false)
  const categories = ref([])

  // 计算属性
  const listedProducts = computed(() => 
    products.value.filter(product => product.is_listed)
  )

  const productCategories = computed(() => {
    const cats = [...new Set(products.value.map(p => p.category).filter(Boolean))]
    return cats.sort()
  })

  // 获取商品列表
  const fetchProducts = async () => {
    try {
      loading.value = true

      // 如果Supabase未配置，使用演示数据
      if (!isSupabaseConfigured) {
        products.value = [...demoProducts]
        return { success: true, data: products.value }
      }

      const { data, error } = await supabase
        .from('products')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        throw error
      }

      products.value = data || []
      return { success: true, data }

    } catch (error) {
      console.error('获取商品列表失败:', error)
      ElMessage.error('获取商品列表失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 创建商品
  const createProduct = async (productData) => {
    try {
      loading.value = true

      // 如果Supabase未配置，显示提示
      if (!isSupabaseConfigured) {
        ElMessage.warning('演示模式下无法保存数据，请配置Supabase以启用完整功能')
        return { success: false, error: new Error('演示模式限制') }
      }

      const { data, error } = await supabase
        .from('products')
        .insert([productData])
        .select()
        .single()

      if (error) {
        throw error
      }

      products.value.unshift(data)
      ElMessage.success('商品创建成功')
      return { success: true, data }

    } catch (error) {
      console.error('创建商品失败:', error)
      ElMessage.error('创建商品失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 更新商品
  const updateProduct = async (id, updates) => {
    try {
      loading.value = true

      // 如果Supabase未配置，显示提示
      if (!isSupabaseConfigured) {
        ElMessage.warning('演示模式下无法保存数据，请配置Supabase以启用完整功能')
        return { success: false, error: new Error('演示模式限制') }
      }

      const { data, error } = await supabase
        .from('products')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        throw error
      }

      const index = products.value.findIndex(p => p.id === id)
      if (index > -1) {
        products.value[index] = data
      }

      ElMessage.success('商品更新成功')
      return { success: true, data }

    } catch (error) {
      console.error('更新商品失败:', error)
      ElMessage.error('更新商品失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 删除商品
  const deleteProduct = async (id) => {
    try {
      loading.value = true

      // 如果Supabase未配置，显示提示
      if (!isSupabaseConfigured) {
        ElMessage.warning('演示模式下无法保存数据，请配置Supabase以启用完整功能')
        return { success: false, error: new Error('演示模式限制') }
      }

      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', id)

      if (error) {
        throw error
      }

      const index = products.value.findIndex(p => p.id === id)
      if (index > -1) {
        products.value.splice(index, 1)
      }

      ElMessage.success('商品删除成功')
      return { success: true }

    } catch (error) {
      console.error('删除商品失败:', error)
      ElMessage.error('删除商品失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 上传商品图片
  const uploadProductImage = async (file, productId) => {
    try {
      const fileExt = file.name.split('.').pop()
      const fileName = `${productId}-${Date.now()}.${fileExt}`
      const filePath = `products/${fileName}`

      const { error: uploadError } = await supabase.storage
        .from('product-images')
        .upload(filePath, file)

      if (uploadError) {
        throw uploadError
      }

      const { data: { publicUrl } } = supabase.storage
        .from('product-images')
        .getPublicUrl(filePath)

      return { success: true, url: publicUrl }
      
    } catch (error) {
      console.error('上传图片失败:', error)
      ElMessage.error('上传图片失败')
      return { success: false, error }
    }
  }

  // 切换商品上架状态
  const toggleProductStatus = async (id, isListed) => {
    return await updateProduct(id, { is_listed: isListed })
  }

  // 根据ID获取商品
  const getProductById = (id) => {
    return products.value.find(p => p.id === id)
  }

  // 搜索商品
  const searchProducts = (query, filters = {}) => {
    let filtered = products.value

    // 文本搜索
    if (query) {
      const searchTerm = query.toLowerCase()
      filtered = filtered.filter(product => 
        product.name.toLowerCase().includes(searchTerm) ||
        (product.category && product.category.toLowerCase().includes(searchTerm))
      )
    }

    // 类别筛选
    if (filters.category) {
      filtered = filtered.filter(product => product.category === filters.category)
    }

    // 状态筛选
    if (filters.status !== undefined) {
      filtered = filtered.filter(product => product.is_listed === filters.status)
    }

    return filtered
  }

  return {
    // 状态
    products,
    loading,
    categories,
    
    // 计算属性
    listedProducts,
    productCategories,
    
    // 方法
    fetchProducts,
    createProduct,
    updateProduct,
    deleteProduct,
    uploadProductImage,
    toggleProductStatus,
    getProductById,
    searchProducts
  }
})
