<template>
  <div class="calendar-view">
    <div class="page-header">
      <h2>生产/配送日历</h2>
      <div class="view-controls">
        <el-radio-group v-model="viewMode">
          <el-radio-button label="month">月视图</el-radio-button>
          <el-radio-button label="week">周视图</el-radio-button>
          <el-radio-button label="year">年视图</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <el-card>
      <div class="calendar-container">
        <el-calendar v-model="currentDate" v-loading="loading">
          <template #date-cell="{ data }">
            <div
              class="calendar-cell"
              @click="handleDateClick(data)"
              :class="{ 'has-orders': getOrdersForDate(data.day).length > 0 }"
            >
              <div class="date-info">
                <span class="solar-date">{{ data.day.split('-')[2] }}</span>
                <span class="lunar-date">{{ getLunarDate(data.day) }}</span>
              </div>
              <div v-if="getOrdersForDate(data.day).length > 0" class="order-info">
                <el-tag size="small" type="primary">
                  {{ getOrdersForDate(data.day).length }}单
                </el-tag>
              </div>
              <div v-if="isHoliday(data.day)" class="holiday-mark">
                <el-tag size="small" type="danger">休</el-tag>
              </div>
            </div>
          </template>
        </el-calendar>
      </div>
    </el-card>

    <!-- 日期详情对话框 -->
    <el-dialog
      v-model="showDateDetail"
      :title="`${selectedDate} 订单详情`"
      width="900px"
      @close="selectedDate = ''"
    >
      <el-table :data="selectedDateOrders" style="width: 100%">
        <el-table-column prop="id" label="订单号" width="100" />
        <el-table-column label="客户">
          <template #default="scope">
            {{ scope.row.customers?.name || '未知客户' }}
          </template>
        </el-table-column>
        <el-table-column label="商品">
          <template #default="scope">
            {{ formatProducts(scope.row.order_items) }}
          </template>
        </el-table-column>
        <el-table-column prop="total_price" label="金额">
          <template #default="scope">
            ¥{{ parseFloat(scope.row.total_price || 0).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="订单状态">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.order_status)">
              {{ getOrderStatusText(scope.row.order_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="送货时间">
          <template #default="scope">
            {{ new Date(scope.row.delivery_datetime).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useOrdersStore } from '../stores/orders'

const ordersStore = useOrdersStore()

const viewMode = ref('month')
const currentDate = ref(new Date())
const showDateDetail = ref(false)
const selectedDate = ref('')
const holidays = ref([])
const loading = ref(false)

// 计算属性
const selectedDateOrders = computed(() => {
  if (!selectedDate.value) return []
  return getOrdersForDate(selectedDate.value)
})

// 按日期分组的订单
const ordersByDate = computed(() => {
  const grouped = {}
  ordersStore.orders.forEach(order => {
    if (order.delivery_datetime) {
      const date = order.delivery_datetime.split('T')[0] // 获取日期部分
      if (!grouped[date]) grouped[date] = []
      grouped[date].push(order)
    }
  })
  return grouped
})

// 获取指定日期的订单
const getOrdersForDate = (date) => {
  return ordersByDate.value[date] || []
}

// 获取农历日期（简化版本）
const getLunarDate = (date) => {
  // 这里可以集成农历转换库，暂时返回简化版本
  const lunarDates = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十']
  const day = new Date(date).getDate()
  return lunarDates[day % 10] || '初一'
}

// 检查是否为节假日
const isHoliday = (date) => {
  return holidays.value.some(holiday => holiday.date === date)
}

// 获取状态类型
const getOrderStatusType = (status) => {
  const statusMap = {
    'pending': 'danger',
    'production': 'info',
    'delivery': 'warning',
    'completed': 'success',
    'cancelled': ''
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'production': '生产中',
    'delivery': '配送中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

// 点击日期单元格
const handleDateClick = (date) => {
  const orders = getOrdersForDate(date.day)
  if (orders.length > 0) {
    selectedDate.value = date.day
    showDateDetail.value = true
  }
}

// 获取节假日数据
const fetchHolidays = async (year) => {
  try {
    loading.value = true

    // 使用免费的节假日API
    const response = await fetch(`https://timor.tech/api/holiday/year/${year}`)
    const data = await response.json()

    if (data.code === 0) {
      holidays.value = Object.entries(data.holiday).map(([date, info]) => ({
        date,
        name: info.name,
        isHoliday: info.holiday
      }))
    }
  } catch (error) {
    console.error('获取节假日数据失败:', error)
    // 使用默认节假日数据
    holidays.value = [
      { date: '2024-01-01', name: '元旦', isHoliday: true },
      { date: '2024-02-10', name: '春节', isHoliday: true },
      { date: '2024-02-11', name: '春节', isHoliday: true },
      { date: '2024-02-12', name: '春节', isHoliday: true },
      { date: '2024-04-04', name: '清明节', isHoliday: true },
      { date: '2024-05-01', name: '劳动节', isHoliday: true },
      { date: '2024-06-10', name: '端午节', isHoliday: true },
      { date: '2024-09-17', name: '中秋节', isHoliday: true },
      { date: '2024-10-01', name: '国庆节', isHoliday: true },
      { date: '2024-10-02', name: '国庆节', isHoliday: true },
      { date: '2024-10-03', name: '国庆节', isHoliday: true }
    ]
  } finally {
    loading.value = false
  }
}

// 格式化商品列表
const formatProducts = (orderItems) => {
  if (!orderItems || orderItems.length === 0) return '无商品'

  return orderItems.map(item => {
    const productName = item.products?.name || '未知商品'
    return `${productName} x${item.quantity}`
  }).join(', ')
}

// 监听当前日期变化，获取对应年份的节假日
watch(currentDate, (newDate) => {
  const year = newDate.getFullYear()
  fetchHolidays(year)
}, { immediate: true })

// 组件挂载时获取数据
onMounted(async () => {
  await ordersStore.fetchOrders()
  const currentYear = new Date().getFullYear()
  await fetchHolidays(currentYear)
})
</script>

<style scoped>
.calendar-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
}

.calendar-container {
  min-height: 600px;
}

.calendar-cell {
  height: 100%;
  padding: 4px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s;
}

.calendar-cell:hover {
  background-color: #f5f7fa;
}

.calendar-cell.has-orders {
  background-color: #ecf5ff;
  border: 1px solid #b3d8ff;
}

.date-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.solar-date {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.lunar-date {
  font-size: 12px;
  color: #909399;
}

.order-info {
  position: absolute;
  top: 4px;
  right: 4px;
}

.holiday-mark {
  position: absolute;
  bottom: 4px;
  right: 4px;
}
</style>
