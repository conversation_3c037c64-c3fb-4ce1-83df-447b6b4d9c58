package database

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"shop-order-backend/internal/config"
	"shop-order-backend/internal/models"

	_ "github.com/go-sql-driver/mysql"
	_ "github.com/mattn/go-sqlite3"
	"xorm.io/xorm"
)

// Init 初始化数据库连接
func Init(cfg *config.Config) (*xorm.Engine, error) {
	var engine *xorm.Engine
	var err error

	switch cfg.Database.Type {
	case "sqlite":
		engine, err = initSQLite(cfg.Database.SQLitePath)
	case "mysql":
		engine, err = initMySQL(cfg.Database)
	default:
		return nil, fmt.Errorf("unsupported database type: %s", cfg.Database.Type)
	}

	if err != nil {
		return nil, err
	}

	// 设置日志级别
	if cfg.GinMode == "debug" {
		engine.ShowSQL(true)
	}

	// 同步数据库结构
	if err := syncTables(engine); err != nil {
		return nil, fmt.Errorf("failed to sync tables: %v", err)
	}

	// 初始化默认数据
	if err := initDefaultData(engine); err != nil {
		log.Printf("Warning: failed to init default data: %v", err)
	}

	return engine, nil
}

// initSQLite 初始化SQLite数据库
func initSQLite(dbPath string) (*xorm.Engine, error) {
	// 确保数据目录存在
	dir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create data directory: %v", err)
	}

	engine, err := xorm.NewEngine("sqlite3", dbPath)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to SQLite: %v", err)
	}

	return engine, nil
}

// initMySQL 初始化MySQL数据库
func initMySQL(cfg config.DatabaseConfig) (*xorm.Engine, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=true&loc=Local",
		cfg.User, cfg.Password, cfg.Host, cfg.Port, cfg.Name, cfg.Charset)

	engine, err := xorm.NewEngine("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MySQL: %v", err)
	}

	return engine, nil
}

// syncTables 同步数据库表结构
func syncTables(engine *xorm.Engine) error {
	tables := []interface{}{
		new(models.User),
		new(models.Customer),
		new(models.Address),
		new(models.Product),
		new(models.Order),
		new(models.OrderItem),
	}

	for _, table := range tables {
		if err := engine.Sync2(table); err != nil {
			return fmt.Errorf("failed to sync table %T: %v", table, err)
		}
	}

	return nil
}

// initDefaultData 初始化默认数据
func initDefaultData(engine *xorm.Engine) error {
	// 检查是否已有用户数据
	count, err := engine.Count(&models.User{})
	if err != nil {
		return err
	}

	// 如果没有用户，创建默认管理员账户
	if count == 0 {
		if err := createDefaultUsers(engine); err != nil {
			return err
		}
	}

	return nil
}

// createDefaultUsers 创建默认用户
func createDefaultUsers(engine *xorm.Engine) error {
	// 这里需要导入密码加密包，稍后实现
	// 暂时使用明文密码，后续会替换为加密密码
	defaultUsers := []*models.User{
		{
			Email:    "<EMAIL>",
			Password: "demo123", // 后续会加密
			Role:     "super_admin",
			IsActive: true,
		},
		{
			Email:    "<EMAIL>",
			Password: "demo123", // 后续会加密
			Role:     "staff",
			IsActive: true,
		},
	}

	for _, user := range defaultUsers {
		if _, err := engine.Insert(user); err != nil {
			return fmt.Errorf("failed to create default user %s: %v", user.Email, err)
		}
		log.Printf("Created default user: %s", user.Email)
	}

	return nil
}
