<script setup>
import { RouterView, useRoute } from 'vue-router'
import { computed } from 'vue'
import Layout from './components/Layout.vue'

const route = useRoute()

// 不需要布局的页面（如登录页）
const noLayoutPages = ['/login']
const showLayout = computed(() => !noLayoutPages.includes(route.path))
</script>

<template>
  <div id="app">
    <!-- 登录页面不使用布局 -->
    <RouterView v-if="!showLayout" />

    <!-- 其他页面使用布局 -->
    <Layout v-else />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f0f2f5;
  overflow: hidden;
}

#app {
  height: 100vh;
  width: 100%;
  max-width: 100vw;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 移动端优化 */
@media (max-width: 768px) {
  html, body {
    overflow-x: hidden;
  }

  #app {
    overflow-x: hidden;
  }
}
</style>
