<template>
  <div class="orders-view">
    <div class="page-header">
      <h2>订单管理</h2>
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        新增订单
      </el-button>
    </div>

    <el-card>
      <div class="search-bar">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchQuery"
              placeholder="搜索订单号或客户姓名"
              prefix-icon="Search"
              clearable
            />
          </el-col>
          <el-col :span="5">
            <el-select v-model="statusFilter" placeholder="订单状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="待处理" value="pending" />
              <el-option label="生产中" value="production" />
              <el-option label="配送中" value="delivery" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select v-model="paymentFilter" placeholder="付款状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="已付款" value="paid" />
              <el-option label="未付款" value="unpaid" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="default"
            />
          </el-col>
        </el-row>
      </div>

      <el-table :data="filteredOrders" style="width: 100%" v-loading="ordersStore.loading">
        <el-table-column prop="id" label="订单号" width="100" />
        <el-table-column label="客户姓名">
          <template #default="scope">
            {{ scope.row.customers?.name || '未知客户' }}
          </template>
        </el-table-column>
        <el-table-column prop="delivery_datetime" label="送货时间">
          <template #default="scope">
            {{ formatDateTime(scope.row.delivery_datetime) }}
          </template>
        </el-table-column>
        <el-table-column prop="total_price" label="总金额">
          <template #default="scope">
            ¥{{ parseFloat(scope.row.total_price || 0).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="payment_status" label="付款状态">
          <template #default="scope">
            <el-tag :type="getPaymentStatusType(scope.row.payment_status)">
              {{ getPaymentStatusText(scope.row.payment_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="order_status" label="订单状态">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.order_status)">
              {{ getOrderStatusText(scope.row.order_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewOrder(scope.row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="editOrder(scope.row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteOrder(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑订单对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingOrder ? '编辑订单' : '新增订单'"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="orderFormRef"
        :model="orderForm"
        :rules="orderRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="选择客户" prop="customer_id">
              <el-select
                v-model="orderForm.customer_id"
                placeholder="请选择客户"
                style="width: 100%"
                filterable
              >
                <el-option
                  v-for="customer in customersStore.customers"
                  :key="customer.id"
                  :label="`${customer.name} (${customer.contact})`"
                  :value="customer.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="送货时间" prop="delivery_datetime">
              <el-date-picker
                v-model="orderForm.delivery_datetime"
                type="datetime"
                placeholder="选择送货时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="送货地址" prop="delivery_address">
          <el-input
            v-model="orderForm.delivery_address"
            type="textarea"
            :rows="2"
            placeholder="请输入送货地址"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="付款状态">
              <el-select v-model="orderForm.payment_status" style="width: 100%">
                <el-option label="未付款" value="unpaid" />
                <el-option label="已付款" value="paid" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单状态">
              <el-select v-model="orderForm.order_status" style="width: 100%">
                <el-option label="待处理" value="pending" />
                <el-option label="生产中" value="production" />
                <el-option label="配送中" value="delivery" />
                <el-option label="已完成" value="completed" />
                <el-option label="已取消" value="cancelled" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注">
          <el-input
            v-model="orderForm.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveOrder" :loading="saving">保存</el-button>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="订单详情"
      width="900px"
      @close="currentOrder = null"
    >
      <div v-if="currentOrder">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ currentOrder.id }}</el-descriptions-item>
          <el-descriptions-item label="客户姓名">{{ currentOrder.customers?.name }}</el-descriptions-item>
          <el-descriptions-item label="联系方式">{{ currentOrder.customers?.contact }}</el-descriptions-item>
          <el-descriptions-item label="送货时间">{{ formatDateTime(currentOrder.delivery_datetime) }}</el-descriptions-item>
          <el-descriptions-item label="送货地址" :span="2">{{ currentOrder.delivery_address }}</el-descriptions-item>
          <el-descriptions-item label="付款状态">
            <el-tag :type="getPaymentStatusType(currentOrder.payment_status)">
              {{ getPaymentStatusText(currentOrder.payment_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getOrderStatusType(currentOrder.order_status)">
              {{ getOrderStatusText(currentOrder.order_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="总金额">¥{{ parseFloat(currentOrder.total_price || 0).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(currentOrder.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentOrder.notes || '无' }}</el-descriptions-item>
        </el-descriptions>

        <h4 style="margin-top: 20px;">订单商品</h4>
        <el-table :data="currentOrder.order_items || []" style="width: 100%">
          <el-table-column label="商品名称">
            <template #default="scope">
              {{ scope.row.products?.name || '商品已删除' }}
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="数量" />
          <el-table-column label="单价">
            <template #default="scope">
              ¥{{ parseFloat(scope.row.price_at_order || 0).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="小计">
            <template #default="scope">
              ¥{{ (parseFloat(scope.row.price_at_order || 0) * parseInt(scope.row.quantity || 0)).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="自定义属性">
            <template #default="scope">
              <span v-if="scope.row.selected_attributes">
                <el-tag
                  v-for="(value, key) in scope.row.selected_attributes"
                  :key="key"
                  size="small"
                  style="margin-right: 5px;"
                >
                  {{ key }}: {{ value }}
                </el-tag>
              </span>
              <span v-else>无</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useOrdersStore } from '../stores/orders'
import { useCustomersStore } from '../stores/customers'
import { useProductsStore } from '../stores/products'

const ordersStore = useOrdersStore()
const customersStore = useCustomersStore()
const productsStore = useProductsStore()

// 状态
const searchQuery = ref('')
const statusFilter = ref('')
const paymentFilter = ref('')
const dateRange = ref([])
const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const editingOrder = ref(null)
const currentOrder = ref(null)
const saving = ref(false)

// 表单数据
const orderForm = reactive({
  customer_id: null,
  delivery_datetime: '',
  delivery_address: '',
  payment_status: 'unpaid',
  order_status: 'pending',
  notes: '',
  items: []
})

// 表单验证规则
const orderRules = {
  customer_id: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  delivery_datetime: [
    { required: true, message: '请选择送货时间', trigger: 'change' }
  ],
  delivery_address: [
    { required: true, message: '请输入送货地址', trigger: 'blur' }
  ]
}

const orderFormRef = ref()

// 计算属性
const filteredOrders = computed(() => {
  return ordersStore.searchOrders(searchQuery.value, {
    orderStatus: statusFilter.value,
    paymentStatus: paymentFilter.value,
    dateRange: dateRange.value
  })
})

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取状态类型
const getOrderStatusType = (status) => {
  const statusMap = {
    'pending': 'danger',
    'production': 'info',
    'delivery': 'warning',
    'completed': 'success',
    'cancelled': ''
  }
  return statusMap[status] || 'info'
}

const getPaymentStatusType = (status) => {
  return status === 'paid' ? 'success' : 'danger'
}

// 获取状态文本
const getOrderStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'production': '生产中',
    'delivery': '配送中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

const getPaymentStatusText = (status) => {
  return status === 'paid' ? '已付款' : '未付款'
}

// 查看订单详情
const viewOrder = (order) => {
  currentOrder.value = order
  showDetailDialog.value = true
}

// 编辑订单
const editOrder = (order) => {
  editingOrder.value = order
  Object.assign(orderForm, {
    customer_id: order.customer_id,
    delivery_datetime: order.delivery_datetime,
    delivery_address: order.delivery_address,
    payment_status: order.payment_status,
    order_status: order.order_status,
    notes: order.notes || '',
    items: order.order_items || []
  })
  showAddDialog.value = true
}

// 删除订单
const deleteOrder = async (order) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除订单 #${order.id} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await ordersStore.deleteOrder(order.id)
  } catch (error) {
    if (error.message) {
      console.error('删除订单失败:', error)
    }
  }
}

// 更新订单状态
const updateOrderStatus = async (order, newStatus) => {
  await ordersStore.updateOrderStatus(order.id, newStatus)
}

// 更新付款状态
const updatePaymentStatus = async (order, newStatus) => {
  await ordersStore.updatePaymentStatus(order.id, newStatus)
}

// 保存订单
const saveOrder = async () => {
  if (!orderFormRef.value) return

  await orderFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        saving.value = true

        // 计算总金额
        const totalPrice = ordersStore.calculateOrderTotal(orderForm.items)

        const orderData = {
          ...orderForm,
          total_price: totalPrice
        }

        let result
        if (editingOrder.value) {
          result = await ordersStore.updateOrder(editingOrder.value.id, orderData)
        } else {
          result = await ordersStore.createOrder(orderData, orderForm.items)
        }

        if (result.success) {
          showAddDialog.value = false
          resetForm()
        }
      } catch (error) {
        console.error('保存订单失败:', error)
      } finally {
        saving.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  editingOrder.value = null
  Object.assign(orderForm, {
    customer_id: null,
    delivery_datetime: '',
    delivery_address: '',
    payment_status: 'unpaid',
    order_status: 'pending',
    notes: '',
    items: []
  })
}

// 组件挂载时获取数据
onMounted(async () => {
  await Promise.all([
    ordersStore.fetchOrders(),
    customersStore.fetchCustomers(),
    productsStore.fetchProducts()
  ])
})
</script>

<style scoped>
.orders-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
}

.search-bar {
  margin-bottom: 20px;
}
</style>
