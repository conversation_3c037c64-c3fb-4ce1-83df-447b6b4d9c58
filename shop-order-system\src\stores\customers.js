import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase, isSupabaseConfigured } from '../lib/supabase'
import { ElMessage } from 'element-plus'
import { demoCustomers } from '../lib/demo-data'

export const useCustomersStore = defineStore('customers', () => {
  // 状态
  const customers = ref([])
  const loading = ref(false)

  // 获取客户列表（包含地址信息）
  const fetchCustomers = async () => {
    try {
      loading.value = true

      // 如果Supabase未配置，使用演示数据
      if (!isSupabaseConfigured) {
        customers.value = [...demoCustomers]
        return { success: true, data: customers.value }
      }

      const { data, error } = await supabase
        .from('customers')
        .select(`
          *,
          addresses (*)
        `)
        .order('created_at', { ascending: false })

      if (error) {
        throw error
      }

      customers.value = data || []
      return { success: true, data }

    } catch (error) {
      console.error('获取客户列表失败:', error)
      ElMessage.error('获取客户列表失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 创建客户
  const createCustomer = async (customerData) => {
    try {
      loading.value = true
      
      const { data, error } = await supabase
        .from('customers')
        .insert([customerData])
        .select()
        .single()

      if (error) {
        throw error
      }

      // 添加空的地址数组
      data.addresses = []
      customers.value.unshift(data)
      
      ElMessage.success('客户创建成功')
      return { success: true, data }
      
    } catch (error) {
      console.error('创建客户失败:', error)
      ElMessage.error('创建客户失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 更新客户
  const updateCustomer = async (id, updates) => {
    try {
      loading.value = true
      
      const { data, error } = await supabase
        .from('customers')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        throw error
      }

      const index = customers.value.findIndex(c => c.id === id)
      if (index > -1) {
        // 保留地址信息
        data.addresses = customers.value[index].addresses
        customers.value[index] = data
      }

      ElMessage.success('客户信息更新成功')
      return { success: true, data }
      
    } catch (error) {
      console.error('更新客户失败:', error)
      ElMessage.error('更新客户失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 删除客户
  const deleteCustomer = async (id) => {
    try {
      loading.value = true
      
      const { error } = await supabase
        .from('customers')
        .delete()
        .eq('id', id)

      if (error) {
        throw error
      }

      const index = customers.value.findIndex(c => c.id === id)
      if (index > -1) {
        customers.value.splice(index, 1)
      }

      ElMessage.success('客户删除成功')
      return { success: true }
      
    } catch (error) {
      console.error('删除客户失败:', error)
      ElMessage.error('删除客户失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 添加地址
  const addAddress = async (customerId, addressData) => {
    try {
      const { data, error } = await supabase
        .from('addresses')
        .insert([{ ...addressData, customer_id: customerId }])
        .select()
        .single()

      if (error) {
        throw error
      }

      // 更新本地数据
      const customer = customers.value.find(c => c.id === customerId)
      if (customer) {
        if (!customer.addresses) customer.addresses = []
        customer.addresses.push(data)
      }

      ElMessage.success('地址添加成功')
      return { success: true, data }
      
    } catch (error) {
      console.error('添加地址失败:', error)
      ElMessage.error('添加地址失败')
      return { success: false, error }
    }
  }

  // 更新地址
  const updateAddress = async (addressId, updates) => {
    try {
      const { data, error } = await supabase
        .from('addresses')
        .update(updates)
        .eq('id', addressId)
        .select()
        .single()

      if (error) {
        throw error
      }

      // 更新本地数据
      customers.value.forEach(customer => {
        if (customer.addresses) {
          const addressIndex = customer.addresses.findIndex(a => a.id === addressId)
          if (addressIndex > -1) {
            customer.addresses[addressIndex] = data
          }
        }
      })

      ElMessage.success('地址更新成功')
      return { success: true, data }
      
    } catch (error) {
      console.error('更新地址失败:', error)
      ElMessage.error('更新地址失败')
      return { success: false, error }
    }
  }

  // 删除地址
  const deleteAddress = async (addressId) => {
    try {
      const { error } = await supabase
        .from('addresses')
        .delete()
        .eq('id', addressId)

      if (error) {
        throw error
      }

      // 更新本地数据
      customers.value.forEach(customer => {
        if (customer.addresses) {
          const addressIndex = customer.addresses.findIndex(a => a.id === addressId)
          if (addressIndex > -1) {
            customer.addresses.splice(addressIndex, 1)
          }
        }
      })

      ElMessage.success('地址删除成功')
      return { success: true }
      
    } catch (error) {
      console.error('删除地址失败:', error)
      ElMessage.error('删除地址失败')
      return { success: false, error }
    }
  }

  // 设置默认地址
  const setDefaultAddress = async (customerId, addressId) => {
    try {
      // 先取消该客户的所有默认地址
      await supabase
        .from('addresses')
        .update({ is_default: false })
        .eq('customer_id', customerId)

      // 设置新的默认地址
      const { data, error } = await supabase
        .from('addresses')
        .update({ is_default: true })
        .eq('id', addressId)
        .select()
        .single()

      if (error) {
        throw error
      }

      // 更新本地数据
      const customer = customers.value.find(c => c.id === customerId)
      if (customer && customer.addresses) {
        customer.addresses.forEach(addr => {
          addr.is_default = addr.id === addressId
        })
      }

      ElMessage.success('默认地址设置成功')
      return { success: true, data }
      
    } catch (error) {
      console.error('设置默认地址失败:', error)
      ElMessage.error('设置默认地址失败')
      return { success: false, error }
    }
  }

  // 搜索客户
  const searchCustomers = (query) => {
    if (!query) return customers.value
    
    const searchTerm = query.toLowerCase()
    return customers.value.filter(customer => 
      customer.name.toLowerCase().includes(searchTerm) ||
      (customer.contact && customer.contact.toLowerCase().includes(searchTerm))
    )
  }

  // 根据ID获取客户
  const getCustomerById = (id) => {
    return customers.value.find(c => c.id === id)
  }

  // 获取客户的默认地址
  const getDefaultAddress = (customerId) => {
    const customer = getCustomerById(customerId)
    return customer?.addresses?.find(addr => addr.is_default)
  }

  return {
    // 状态
    customers,
    loading,
    
    // 方法
    fetchCustomers,
    createCustomer,
    updateCustomer,
    deleteCustomer,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
    searchCustomers,
    getCustomerById,
    getDefaultAddress
  }
})
